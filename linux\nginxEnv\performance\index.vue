<template>
	<page-container ref="pageContainer" :is-back="true" title="性能调整">
		<view class="performance-container">
			<!-- 配置预设卡片 -->
			<view class="preset-card">
				<view class="card-header">
					<view class="header-info">
						<uv-icon name="tags" size="24" color="#20a50a"></uv-icon>
						<text class="header-title">配置预设</text>
					</view>
					<text class="header-desc">根据服务器配置快速应用推荐设置</text>
				</view>

				<view class="preset-content">
					<view
						v-for="(preset, key) in configPresets"
						:key="key"
						class="preset-item"
						@tap="applyPreset(key)"
						hover-class="preset-hover"
					>
						<view class="preset-info">
							<text class="preset-name">{{ preset.name }}</text>
							<text class="preset-desc">{{ preset.description }}</text>
						</view>
						<uv-icon name="arrow-rightward" size="16" color="#999999"></uv-icon>
					</view>
				</view>
			</view>
			<!-- 服务配置卡片 -->
			<view class="config-card">
				<view class="card-header">
					<view class="header-info">
						<uv-icon name="setting-fill" size="24" color="#20a50a"></uv-icon>
						<text class="header-title">服务配置</text>
					</view>
					<text class="header-desc">基础服务参数配置</text>
				</view>

				<view class="config-content">
					<!-- Worker进程数 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">worker_processes</text>
							<text class="item-desc">处理进程数，auto表示自动检测核心数</text>
						</view>
						<view class="item-control">
							<input
								class="config-input"
								v-model="formData.worker_processes"
								placeholder="auto"
								@blur="validateField('worker_processes')"
								:class="{ 'input-error': errors.worker_processes }"
							/>
						</view>
						<text v-if="errors.worker_processes" class="error-text">{{ errors.worker_processes }}</text>
					</view>

					<!-- Worker连接数 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">worker_connections</text>
							<text class="item-desc">最大并发连接数</text>
						</view>
						<view class="item-control">
							<input
								class="config-input"
								type="number"
								v-model="formData.worker_connections"
								placeholder="51200"
								@blur="validateField('worker_connections')"
								:class="{ 'input-error': errors.worker_connections }"
							/>
						</view>
						<text v-if="errors.worker_connections" class="error-text">{{ errors.worker_connections }}</text>
					</view>

					<!-- 保持连接超时 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">keepalive_timeout</text>
							<text class="item-desc">连接超时时间</text>
						</view>
						<view class="item-control">
							<view class="input-with-unit">
								<input
									class="config-input"
									type="number"
									v-model="formData.keepalive_timeout"
									placeholder="60"
									@blur="validateField('keepalive_timeout')"
									:class="{ 'input-error': errors.keepalive_timeout }"
								/>
								<text class="unit">秒</text>
							</view>
						</view>
						<text v-if="errors.keepalive_timeout" class="error-text">{{ errors.keepalive_timeout }}</text>
					</view>
				</view>
			</view>

			<!-- 性能调整卡片 -->
			<view class="config-card">
				<view class="card-header">
					<view class="header-info">
						<uv-icon name="play-right-fill" size="24" color="#20a50a"></uv-icon>
						<text class="header-title">性能调整</text>
					</view>
					<text class="header-desc">压缩和缓冲区优化配置</text>
				</view>

				<view class="config-content">
					<!-- Gzip压缩 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">gzip</text>
							<text class="item-desc">是否开启压缩传输</text>
						</view>
						<view class="item-control">
							<uv-switch
								:model-value="formData.gzip"
								size="24"
								activeColor="#20a50a"
								@change="handleSwitchChange('gzip', $event)"
							></uv-switch>
						</view>
					</view>

					<!-- Gzip最小长度 -->
					<view class="config-item" v-if="formData.gzip">
						<view class="item-header">
							<text class="item-label">gzip_min_length</text>
							<text class="item-desc">最小压缩文件</text>
						</view>
						<view class="item-control">
							<view class="input-with-unit">
								<input
									class="config-input"
									type="number"
									v-model="formData.gzip_min_length"
									placeholder="1"
									@blur="validateField('gzip_min_length')"
									:class="{ 'input-error': errors.gzip_min_length }"
								/>
								<text class="unit">KB</text>
							</view>
						</view>
						<text v-if="errors.gzip_min_length" class="error-text">{{ errors.gzip_min_length }}</text>
					</view>

					<!-- Gzip压缩率 -->
					<view class="config-item" v-if="formData.gzip">
						<view class="item-header">
							<text class="item-label">gzip_comp_level</text>
							<text class="item-desc">压缩率</text>
						</view>
						<view class="item-control">
							<input
								class="config-input"
								type="number"
								v-model="formData.gzip_comp_level"
								placeholder="2"
								@blur="validateField('gzip_comp_level')"
								:class="{ 'input-error': errors.gzip_comp_level }"
							/>
						</view>
						<text v-if="errors.gzip_comp_level" class="error-text">{{ errors.gzip_comp_level }}</text>
					</view>

					<!-- 客户端最大请求体 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">client_max_body_size</text>
							<text class="item-desc">最大上传文件</text>
						</view>
						<view class="item-control">
							<view class="input-with-unit">
								<input
									class="config-input"
									type="number"
									v-model="formData.client_max_body_size"
									placeholder="50"
									@blur="validateField('client_max_body_size')"
									:class="{ 'input-error': errors.client_max_body_size }"
								/>
								<text class="unit">MB</text>
							</view>
						</view>
						<text v-if="errors.client_max_body_size" class="error-text">{{ errors.client_max_body_size }}</text>
					</view>
				</view>
			</view>

			<!-- 缓冲区配置卡片 -->
			<view class="config-card">
				<view class="card-header">
					<view class="header-info">
						<uv-icon name="grid-fill" size="24" color="#20a50a"></uv-icon>
						<text class="header-title">缓冲区配置</text>
					</view>
					<text class="header-desc">服务器缓冲区大小设置</text>
				</view>

				<view class="config-content">
					<!-- 服务器名称哈希桶大小 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">server_names_hash_bucket_size</text>
							<text class="item-desc">服务器名字的hash表大小</text>
						</view>
						<view class="item-control">
							<input
								class="config-input"
								type="number"
								v-model="formData.server_names_hash_bucket_size"
								placeholder="512"
								@blur="validateField('server_names_hash_bucket_size')"
								:class="{ 'input-error': errors.server_names_hash_bucket_size }"
							/>
						</view>
						<text v-if="errors.server_names_hash_bucket_size" class="error-text">{{ errors.server_names_hash_bucket_size }}</text>
					</view>

					<!-- 客户端请求头缓冲区 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">client_header_buffer_size</text>
							<text class="item-desc">客户端请求头buffer大小</text>
						</view>
						<view class="item-control">
							<view class="input-with-unit">
								<input
									class="config-input"
									type="number"
									v-model="formData.client_header_buffer_size"
									placeholder="32"
									@blur="validateField('client_header_buffer_size')"
									:class="{ 'input-error': errors.client_header_buffer_size }"
								/>
								<text class="unit">KB</text>
							</view>
						</view>
						<text v-if="errors.client_header_buffer_size" class="error-text">{{ errors.client_header_buffer_size }}</text>
					</view>

					<!-- 客户端请求体缓冲区 -->
					<view class="config-item">
						<view class="item-header">
							<text class="item-label">client_body_buffer_size</text>
							<text class="item-desc">请求主体缓冲区</text>
						</view>
						<view class="item-control">
							<view class="input-with-unit">
								<input
									class="config-input"
									type="number"
									v-model="formData.client_body_buffer_size"
									placeholder="512"
									@blur="validateField('client_body_buffer_size')"
									:class="{ 'input-error': errors.client_body_buffer_size }"
								/>
								<text class="unit">KB</text>
							</view>
						</view>
						<text v-if="errors.client_body_buffer_size" class="error-text">{{ errors.client_body_buffer_size }}</text>
					</view>
				</view>
			</view>

			<!-- 性能建议 -->
			<view class="info-card" v-if="performanceAdvice.length > 0">
				<view class="info-header">
					<uv-icon name="warning-fill" size="20" color="#f59e0b"></uv-icon>
					<text class="info-title">性能建议</text>
				</view>
				<view class="info-content">
					<text
						v-for="(advice, index) in performanceAdvice"
						:key="index"
						class="advice-item"
					>
						• {{ advice }}
					</text>
				</view>
			</view>

			<!-- 配置说明 -->
			<view class="info-card">
				<view class="info-header">
					<uv-icon name="info-circle-fill" size="20" color="#20a50a"></uv-icon>
					<text class="info-title">配置说明</text>
				</view>
				<view class="info-content">
					<text class="info-item">• worker_processes 建议设置为 auto 或 CPU 核心数</text>
					<text class="info-item">• worker_connections 影响并发处理能力，建议根据服务器性能调整</text>
					<text class="info-item">• 开启 gzip 压缩可以有效减少传输数据量</text>
					<text class="info-item">• 缓冲区大小影响内存使用，请根据实际需求调整</text>
					<text class="info-item">• 修改配置后需要重载 Nginx 配置才能生效</text>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-section">
				<view class="action-row">
					<button
						class="action-button secondary"
						@click="handleExportConfig"
						:disabled="loading"
					>
						<uv-icon name="download" size="18" color="#666666"></uv-icon>
						<text>导出配置</text>
					</button>
					<button
						class="action-button secondary"
						@click="handleImportConfig"
						:disabled="loading"
					>
						<uv-icon name="arrow-up" size="18" color="#666666"></uv-icon>
						<text>导入配置</text>
					</button>
				</view>
				<view class="action-row">
					<button
						class="action-button secondary"
						@click="handleReset"
						:disabled="loading"
					>
						<uv-icon name="reload" size="20" color="#666666"></uv-icon>
						<text>重置默认</text>
					</button>
					<button
						class="action-button primary"
						@click="handleSave"
						:disabled="loading"
						:class="{ 'button-loading': loading }"
					>
						<uv-loading-icon v-if="loading" mode="circle" size="20" color="#ffffff"></uv-loading-icon>
						<text v-else>保存配置</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 重置确认对话框 -->
		<CustomDialog
			v-model="showResetDialog"
			title="确认重置"
			confirmText="确认重置"
			cancelText="取消"
			contentHeight="200rpx"
			@confirm="confirmReset"
			@cancel="showResetDialog = false"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				确定要重置为默认配置吗？当前修改将会丢失。
			</view>
		</CustomDialog>

		<!-- 预设应用确认对话框 -->
		<CustomDialog
			v-model="showPresetDialog"
			:title="presetDialogTitle"
			confirmText="确认应用"
			cancelText="取消"
			contentHeight="200rpx"
			@confirm="confirmApplyPreset"
			@cancel="showPresetDialog = false"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{ presetDialogContent }}
			</view>
		</CustomDialog>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import { onShow } from '@dcloudio/uni-app';
	import { computed } from 'vue';
	import {
		pageContainer,
		formData,
		errors,
		loading,
		configPresets,
		showResetDialog,
		showPresetDialog,
		presetDialogTitle,
		presetDialogContent,
		validateField,
		handleSwitchChange,
		handleSave,
		handleReset,
		confirmReset,
		applyPreset,
		confirmApplyPreset,
		getPerformanceAdvice,
		initPerformanceData
	} from './useController.js';

	// 计算性能建议
	const performanceAdvice = computed(() => {
		return getPerformanceAdvice();
	});

	onShow(async () => {
		await initPerformanceData();
	});
</script>

<style lang="scss" scoped>
	.performance-container {
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		background-color: var(--page-bg-color);
	}

	/* 配置卡片 */
	.config-card,
	.preset-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
	}

	.card-header {
		padding: 32rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);

		.header-info {
			display: flex;
			align-items: center;
			gap: 16rpx;
			margin-bottom: 8rpx;

			.header-title {
				font-size: 32rpx;
				font-weight: 600;
				color: var(--text-primary-color);
			}
		}

		.header-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			margin-left: 40rpx;
		}
	}

	.config-content {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
	}

	/* 配置项 */
	.config-item {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.item-header {
		display: flex;
		flex-direction: column;
		gap: 8rpx;

		.item-label {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-primary-color);
		}

		.item-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			line-height: 1.4;
		}
	}

	.item-control {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	/* 输入框样式 */
	.config-input {
		flex: 1;
		height: 88rpx;
		padding: 0 24rpx;
		background-color: var(--input-bg-color);
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		font-size: 28rpx;
		color: var(--text-primary-color);
		transition: all 0.2s ease;

		&:focus {
			border-color: #20a50a;
			background-color: rgba(32, 165, 10, 0.02);
		}

		&.input-error {
			border-color: #dc2626;
			background-color: rgba(220, 38, 38, 0.02);
		}
	}

	.input-with-unit {
		display: flex;
		align-items: center;
		background-color: var(--input-bg-color);
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		overflow: hidden;
		transition: all 0.2s ease;

		&:focus-within {
			border-color: #20a50a;
			background-color: rgba(32, 165, 10, 0.02);
		}

		.config-input {
			border: none;
			background: transparent;
			border-radius: 0;

			&:focus {
				border: none;
				background: transparent;
			}
		}

		.unit {
			padding: 0 24rpx;
			font-size: 24rpx;
			color: var(--text-secondary-color);
			background-color: rgba(0, 0, 0, 0.02);
			border-left: 2rpx solid var(--border-color);
			height: 84rpx;
			display: flex;
			align-items: center;
		}
	}

	/* 错误提示 */
	.error-text {
		font-size: 22rpx;
		color: #dc2626;
		margin-top: 8rpx;
	}

	/* 信息卡片 */
	.info-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
	}

	.info-header {
		padding: 24rpx 32rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		gap: 12rpx;

		.info-title {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-primary-color);
		}
	}

	.info-content {
		padding: 24rpx 32rpx;
		display: flex;
		flex-direction: column;
		gap: 16rpx;

		.info-item {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			line-height: 1.5;
		}

		.advice-item {
			font-size: 24rpx;
			color: #f59e0b;
			line-height: 1.5;
			background-color: rgba(245, 158, 11, 0.05);
			padding: 12rpx 16rpx;
			border-radius: 8rpx;
			border-left: 4rpx solid #f59e0b;
		}
	}

	/* 预设配置 */
	.preset-content {
		padding: 24rpx 32rpx 32rpx;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.preset-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		background-color: rgba(32, 165, 10, 0.02);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
		border-radius: 12rpx;
		transition: all 0.2s ease;

		.preset-info {
			display: flex;
			flex-direction: column;
			gap: 8rpx;

			.preset-name {
				font-size: 28rpx;
				font-weight: 500;
				color: var(--text-primary-color);
			}

			.preset-desc {
				font-size: 24rpx;
				color: var(--text-secondary-color);
				line-height: 1.4;
			}
		}
	}

	.preset-hover {
		background-color: rgba(32, 165, 10, 0.05);
		border-color: rgba(32, 165, 10, 0.2);
		transform: translateY(-2rpx);
	}

	/* 操作按钮区域 */
	.action-section {
		padding: 32rpx 0;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.action-row {
		display: flex;
		gap: 16rpx;
	}

	.action-button {
		flex: 1;
		height: 88rpx;
		border-radius: 16rpx;
		font-size: 28rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		transition: all 0.2s ease;

		&.secondary {
			background-color: var(--dialog-bg-color);
			border: 2rpx solid var(--border-color);
			color: var(--text-secondary-color);

			&:active {
				transform: translateY(2rpx);
				background-color: rgba(0, 0, 0, 0.02);
			}
		}

		&.primary {
			background: linear-gradient(135deg, #20a50a 0%, #16a34a 100%);
			border: none;
			color: #ffffff;
			font-weight: 600;
			box-shadow: 0 6rpx 20rpx rgba(32, 165, 10, 0.25);

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 3rpx 10rpx rgba(32, 165, 10, 0.25);
			}
		}

		&.button-loading {
			opacity: 0.7;
			pointer-events: none;
		}

		&:disabled {
			opacity: 0.5;
			pointer-events: none;
		}
	}

	/* 响应式调整 */
	@media (max-width: 360px) {
		.performance-container {
			padding: 16rpx;
			gap: 16rpx;
		}

		.config-content {
			padding: 24rpx;
			gap: 24rpx;
		}

		.card-header {
			padding: 24rpx;
		}

		.action-section {
			gap: 12rpx;

			.action-row {
				flex-direction: column;
				gap: 12rpx;

				.action-button {
					flex: none;
				}
			}
		}
	}
</style>
