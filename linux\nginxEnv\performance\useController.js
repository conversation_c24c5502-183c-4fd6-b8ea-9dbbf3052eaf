import { ref, reactive } from 'vue';

// 页面引用
export const pageContainer = ref(null);

// 表单数据
export const formData = reactive({
	// 服务配置
	worker_processes: 'auto',
	worker_connections: 51200,
	keepalive_timeout: 60,

	// 性能调整
	gzip: true,
	gzip_min_length: 1,
	gzip_comp_level: 2,
	client_max_body_size: 50,

	// 缓冲区配置
	server_names_hash_bucket_size: 512,
	client_header_buffer_size: 32,
	client_body_buffer_size: 512,
});

// 表单验证错误
export const errors = reactive({});

// 加载状态
export const loading = ref(false);

// 对话框状态
export const showResetDialog = ref(false);
export const showPresetDialog = ref(false);
export const presetDialogTitle = ref('');
export const presetDialogContent = ref('');
export const currentPresetKey = ref('');

// 验证规则
const validationRules = {
	worker_processes: {
		required: false,
		validator: (value) => {
			if (!value) return true; // 允许为空
			if (value === 'auto') return true;
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 64) {
				return '请输入1-64之间的数字或auto';
			}
			return true;
		},
	},
	worker_connections: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1024 || num > 65535) {
				return '请输入1024-65535之间的数字';
			}
			return true;
		},
	},
	keepalive_timeout: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 3600) {
				return '请输入1-3600之间的数字';
			}
			return true;
		},
	},
	gzip_min_length: {
		required: false,
		validator: (value) => {
			if (!formData.gzip) return true; // gzip关闭时不验证
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 1024) {
				return '请输入1-1024之间的数字';
			}
			return true;
		},
	},
	gzip_comp_level: {
		required: false,
		validator: (value) => {
			if (!formData.gzip) return true; // gzip关闭时不验证
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 9) {
				return '请输入1-9之间的数字';
			}
			return true;
		},
	},
	client_max_body_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 1024) {
				return '请输入1-1024之间的数字';
			}
			return true;
		},
	},
	server_names_hash_bucket_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 32 || num > 1024) {
				return '请输入32-1024之间的数字';
			}
			// 检查是否为2的幂
			if ((num & (num - 1)) !== 0) {
				return '请输入2的幂次方数字(如32, 64, 128, 256, 512, 1024)';
			}
			return true;
		},
	},
	client_header_buffer_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 64) {
				return '请输入1-64之间的数字';
			}
			return true;
		},
	},
	client_body_buffer_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 8 || num > 1024) {
				return '请输入8-1024之间的数字';
			}
			return true;
		},
	},
};

// 字段验证
export const validateField = (fieldName) => {
	const rule = validationRules[fieldName];
	if (!rule) return true;

	const value = formData[fieldName];

	// 必填验证
	if (rule.required && (!value || value === '')) {
		errors[fieldName] = `${getFieldLabel(fieldName)}不能为空`;
		return false;
	}

	// 自定义验证
	if (rule.validator) {
		const result = rule.validator(value);
		if (result !== true) {
			errors[fieldName] = result;
			return false;
		}
	}

	// 验证通过，清除错误
	delete errors[fieldName];
	return true;
};

// 获取字段标签
const getFieldLabel = (fieldName) => {
	const labels = {
		worker_processes: 'Worker进程数',
		worker_connections: 'Worker连接数',
		keepalive_timeout: '保持连接超时',
		gzip_min_length: 'Gzip最小长度',
		gzip_comp_level: 'Gzip压缩率',
		client_max_body_size: '客户端最大请求体',
		server_names_hash_bucket_size: '服务器名称哈希桶大小',
		client_header_buffer_size: '客户端请求头缓冲区',
		client_body_buffer_size: '客户端请求体缓冲区',
	};
	return labels[fieldName] || fieldName;
};

// 验证所有字段
const validateAllFields = () => {
	let isValid = true;
	Object.keys(validationRules).forEach((fieldName) => {
		if (!validateField(fieldName)) {
			isValid = false;
		}
	});
	return isValid;
};

// 开关变化处理
export const handleSwitchChange = (fieldName, value) => {
	formData[fieldName] = value;

	// 如果关闭gzip，清除相关字段的错误
	if (fieldName === 'gzip' && !value) {
		delete errors.gzip_min_length;
		delete errors.gzip_comp_level;
	}
};

// 初始化性能数据
export const initPerformanceData = async () => {
	try {
		loading.value = true;
		// 模拟API调用获取当前配置
		await mockGetPerformanceConfig();
	} catch (error) {
		console.error('初始化性能数据失败:', error);
		pageContainer.value?.notify?.error('获取配置失败，请重试');
	} finally {
		loading.value = false;
	}
};

// 模拟API - 获取性能配置
const mockGetPerformanceConfig = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟从服务器获取的配置数据
			const mockConfig = {
				worker_processes: 'auto',
				worker_connections: 51200,
				keepalive_timeout: 60,
				gzip: true,
				gzip_min_length: 1,
				gzip_comp_level: 2,
				client_max_body_size: 50,
				server_names_hash_bucket_size: 512,
				client_header_buffer_size: 32,
				client_body_buffer_size: 512,
			};

			// 更新表单数据
			Object.assign(formData, mockConfig);
			resolve(mockConfig);
		}, 500);
	});
};

// 保存配置
export const handleSave = async () => {
	try {
		// 验证所有字段
		if (!validateAllFields()) {
			pageContainer.value?.notify?.error('请检查输入内容');
			return;
		}

		loading.value = true;
		pageContainer.value?.notify?.info('正在保存配置...');

		// 模拟API调用保存配置
		await mockSavePerformanceConfig();

		pageContainer.value?.notify?.success('配置保存成功');
	} catch (error) {
		console.error('保存配置失败:', error);
		pageContainer.value?.notify?.error('保存失败，请重试');
	} finally {
		loading.value = false;
	}
};

// 模拟API - 保存性能配置
const mockSavePerformanceConfig = async () => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			// 模拟保存成功
			console.log('保存的配置数据:', formData);
			resolve({ status: true, msg: '配置保存成功' });
		}, 1000);
	});
};

// 重置配置到默认值
export const handleReset = () => {
	showResetDialog.value = true;
};

export const confirmReset = () => {
	resetToDefault();
	showResetDialog.value = false;
};

const resetToDefault = () => {
	const defaultConfig = {
		worker_processes: 'auto',
		worker_connections: 51200,
		keepalive_timeout: 60,
		gzip: true,
		gzip_min_length: 1,
		gzip_comp_level: 2,
		client_max_body_size: 50,
		server_names_hash_bucket_size: 512,
		client_header_buffer_size: 32,
		client_body_buffer_size: 512,
	};

	Object.assign(formData, defaultConfig);

	// 清除所有错误
	Object.keys(errors).forEach((key) => {
		delete errors[key];
	});

	pageContainer.value?.notify?.success('已重置为默认配置');
};

// 配置预设
export const configPresets = {
	low: {
		name: '低配置服务器',
		description: '适用于1-2核CPU，1-2GB内存的服务器',
		config: {
			worker_processes: 1,
			worker_connections: 1024,
			keepalive_timeout: 30,
			gzip: true,
			gzip_min_length: 1,
			gzip_comp_level: 1,
			client_max_body_size: 10,
			server_names_hash_bucket_size: 64,
			client_header_buffer_size: 16,
			client_body_buffer_size: 128,
		},
	},
	medium: {
		name: '中等配置服务器',
		description: '适用于2-4核CPU，2-8GB内存的服务器',
		config: {
			worker_processes: 'auto',
			worker_connections: 2048,
			keepalive_timeout: 60,
			gzip: true,
			gzip_min_length: 1,
			gzip_comp_level: 2,
			client_max_body_size: 50,
			server_names_hash_bucket_size: 128,
			client_header_buffer_size: 32,
			client_body_buffer_size: 256,
		},
	},
	high: {
		name: '高配置服务器',
		description: '适用于4核以上CPU，8GB以上内存的服务器',
		config: {
			worker_processes: 'auto',
			worker_connections: 4096,
			keepalive_timeout: 75,
			gzip: true,
			gzip_min_length: 1,
			gzip_comp_level: 3,
			client_max_body_size: 100,
			server_names_hash_bucket_size: 256,
			client_header_buffer_size: 64,
			client_body_buffer_size: 512,
		},
	},
};

// 应用配置预设
export const applyPreset = (presetKey) => {
	const preset = configPresets[presetKey];
	if (!preset) return;

	currentPresetKey.value = presetKey;
	presetDialogTitle.value = '应用预设配置';
	presetDialogContent.value = `确定要应用"${preset.name}"预设吗？当前配置将被覆盖。`;
	showPresetDialog.value = true;
};

export const confirmApplyPreset = () => {
	const preset = configPresets[currentPresetKey.value];
	if (preset) {
		Object.assign(formData, preset.config);

		// 清除所有错误
		Object.keys(errors).forEach((key) => {
			delete errors[key];
		});

		pageContainer.value?.notify?.success(`已应用${preset.name}预设`);
	}
	showPresetDialog.value = false;
};

// 获取性能建议
export const getPerformanceAdvice = () => {
	const advice = [];

	// Worker进程数建议
	if (formData.worker_processes !== 'auto' && parseInt(formData.worker_processes) > 8) {
		advice.push('建议将worker_processes设置为auto或不超过CPU核心数');
	}

	// 连接数建议
	if (formData.worker_connections > 4096) {
		advice.push('worker_connections设置过高可能会消耗过多内存');
	}

	// Gzip建议
	if (!formData.gzip) {
		advice.push('建议开启gzip压缩以减少传输数据量');
	} else if (formData.gzip_comp_level > 6) {
		advice.push('gzip压缩率过高会增加CPU负载，建议设置为1-6');
	}

	// 缓冲区建议
	if (formData.client_max_body_size > 100) {
		advice.push('client_max_body_size设置过大可能存在安全风险');
	}

	return advice;
};

// 导出配置
export const handleExportConfig = () => {
	try {
		const configData = {
			...formData,
			exportTime: new Date().toISOString(),
			version: '1.0',
		};

		const configJson = JSON.stringify(configData, null, 2);
		const fileName = `nginx_performance_config_${new Date().toISOString().slice(0, 10)}.json`;

		// 在uni-app中，我们可以使用uni.setClipboardData来复制配置
		uni.setClipboardData({
			data: configJson,
			success: () => {
				pageContainer.value?.notify?.success('配置已复制到剪贴板，可以保存为文件');
			},
			fail: () => {
				pageContainer.value?.notify?.error('导出失败，请重试');
			},
		});
	} catch (error) {
		console.error('导出配置失败:', error);
		pageContainer.value?.notify?.error('导出失败，请重试');
	}
};

// 导入配置
export const handleImportConfig = () => {
	uni.showModal({
		title: '导入配置',
		content: '请将配置JSON粘贴到剪贴板，然后点击确认导入',
		success: (res) => {
			if (res.confirm) {
				importFromClipboard();
			}
		},
	});
};

const importFromClipboard = () => {
	uni.getClipboardData({
		success: (res) => {
			try {
				const configData = JSON.parse(res.data);

				// 验证配置数据格式
				if (!validateImportConfig(configData)) {
					pageContainer.value?.notify?.error('配置格式不正确，请检查导入的数据');
					return;
				}

				// 应用导入的配置
				Object.keys(formData).forEach((key) => {
					if (configData.hasOwnProperty(key)) {
						formData[key] = configData[key];
					}
				});

				// 清除所有错误
				Object.keys(errors).forEach((key) => {
					delete errors[key];
				});

				pageContainer.value?.notify?.success('配置导入成功');
			} catch (error) {
				console.error('解析配置失败:', error);
				pageContainer.value?.notify?.error('配置格式错误，请检查导入的JSON数据');
			}
		},
		fail: () => {
			pageContainer.value?.notify?.error('读取剪贴板失败，请重试');
		},
	});
};

// 验证导入的配置数据
const validateImportConfig = (config) => {
	const requiredFields = [
		'worker_processes',
		'worker_connections',
		'keepalive_timeout',
		'gzip',
		'client_max_body_size',
		'server_names_hash_bucket_size',
		'client_header_buffer_size',
		'client_body_buffer_size',
	];

	return requiredFields.every((field) => config.hasOwnProperty(field));
};
